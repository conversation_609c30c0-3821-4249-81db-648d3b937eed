import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  DialogContent,
  Box,
  IconButton,
  Typography,
  TextField,
  Button,
  CircularProgress,
  Card,
  CardMedia,
  CardActionArea,
  useTheme
} from '@mui/material';
import {
  Close as CloseIcon,
  Search as SearchIcon
} from '@mui/icons-material';

interface UnsplashImage {
  id: string;
  urls: {
    small: string;
    regular: string;
    full: string;
  };
  alt_description: string;
  user: {
    name: string;
  };
}

interface ImagePickerDialogProps {
  open: boolean;
  onClose: () => void;
  onImageSelect: (imageUrl: string) => void;
  title?: string;
}

const ImagePickerDialog: React.FC<ImagePickerDialogProps> = ({
  open,
  onClose,
  onImageSelect,
  title = "Choose a cover image"
}) => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [images, setImages] = useState<UnsplashImage[]>([]);
  const [loading, setLoading] = useState(false);

  const UNSPLASH_ACCESS_KEY = process.env.REACT_APP_UNSPLASH_ACCESS_KEY;

  const popularSearches = [
    'trading charts',
    'financial markets',
    'business success',
    'growth analytics',
    'stock market',
    'cryptocurrency',
    'investment',
    'profit growth'
  ];

  const generatePlaceholderImages = (query: string): UnsplashImage[] => {
    const categories = ['trading', 'finance', 'business', 'charts', 'success', 'growth'];
    const selectedCategory = categories.find(cat => query.toLowerCase().includes(cat)) || 'business';
    
    return Array.from({ length: 24 }, (_, i) => ({
      id: `placeholder-${i}`,
      urls: {
        small: `https://picsum.photos/400/200?random=${i}&blur=1`,
        regular: `https://picsum.photos/800/400?random=${i}`,
        full: `https://picsum.photos/1200/600?random=${i}`
      },
      alt_description: `${selectedCategory} image ${i + 1}`,
      user: {
        name: 'Demo User'
      }
    }));
  };

  const handleSearchImages = async (query: string = searchQuery) => {
    if (!query.trim()) return;

    setLoading(true);
    try {
      if (!UNSPLASH_ACCESS_KEY) {
        console.warn('Unsplash API key not configured, using placeholder images');
        setImages(generatePlaceholderImages(query));
        return;
      }

      const response = await fetch(
        `https://api.unsplash.com/search/photos?query=${encodeURIComponent(query)}&per_page=24&orientation=landscape`,
        {
          headers: {
            Authorization: `Client-ID ${UNSPLASH_ACCESS_KEY}`,
          },
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        setImages(data.results);
      } else {
        console.error('Failed to fetch images from Unsplash');
        setImages(generatePlaceholderImages(query));
      }
    } catch (error) {
      console.error('Error fetching images:', error);
      setImages(generatePlaceholderImages(query));
    } finally {
      setLoading(false);
    }
  };

  const handleImageClick = (image: UnsplashImage) => {
    onImageSelect(image.urls.regular);
    onClose();
  };

  const handlePopularSearchClick = (search: string) => {
    setSearchQuery(search);
    handleSearchImages(search);
  };

  // Load default images when dialog opens
  React.useEffect(() => {
    if (open && images.length === 0) {
      setSearchQuery('trading charts');
      handleSearchImages('trading charts');
    }
  }, [open]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '85vh'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        pb: 1
      }}>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          {title}
        </Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 1 }}>
        {/* Search */}
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Search for images..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSearchImages()}
            slotProps={{
              input: {
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }
            }}
          />
          <Button
            variant="contained"
            onClick={() => handleSearchImages()}
            disabled={loading}
            sx={{ minWidth: 100 }}
          >
            {loading ? <CircularProgress size={20} /> : 'Search'}
          </Button>
        </Box>

        {/* Popular searches */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="caption" sx={{ color: 'text.secondary', mb: 1, display: 'block' }}>
            Popular searches:
          </Typography>
          <Box sx={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: 0.5
          }}>
            {popularSearches.map((search) => (
              <Button
                key={search}
                size="small"
                variant="outlined"
                onClick={() => handlePopularSearchClick(search)}
                sx={{
                  fontSize: '0.75rem',
                  textTransform: 'none',
                  borderRadius: 2,
                  minWidth: 'auto',
                  px: 1.5,
                  '&:hover': {
                    borderColor: 'primary.main',
                    backgroundColor: 'action.hover'
                  }
                }}
              >
                {search}
              </Button>
            ))}
          </Box>
        </Box>

        {/* Images Grid */}
        <Box sx={{
          maxHeight: '400px',
          overflow: 'auto',
          scrollbarWidth: 'thin',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'transparent',
          },
          '&::-webkit-scrollbar-thumb': {
            background: 'rgba(0,0,0,0.2)',
            borderRadius: '3px',
          }
        }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: {
                  xs: 'repeat(2, 1fr)',
                  sm: 'repeat(3, 1fr)',
                  md: 'repeat(4, 1fr)'
                },
                gap: 1
              }}
            >
              {images.map((image) => (
                  <Card
                    sx={{
                      borderRadius: 2,
                      transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 3
                      }
                    }}
                  >
                    <CardActionArea onClick={() => handleImageClick(image)}>
                      <CardMedia
                        component="img"
                        height="120"
                        image={image.urls.small}
                        alt={image.alt_description || 'Cover image'}
                        sx={{
                          objectFit: 'cover'
                        }}
                      />
                    </CardActionArea>
                  </Card>
              ))}
            </Box>
          )}
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default ImagePickerDialog;
